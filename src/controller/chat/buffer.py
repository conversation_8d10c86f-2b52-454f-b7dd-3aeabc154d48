import re
from collections import deque
from collections.abc import AsyncIterable
from enum import Enum
from typing import List, Tuple

from pydantic import BaseModel


class BufferType(Enum):
    SENSITIVE = "sensitive"
    CORNER = "corner"
    CONTENT = "content"
    THINK_CONTENT = "think_content"
    THINK_CONTENT_CORNER = "think_content_corner"


class Buffer(BaseModel):
    content: str
    type_: BufferType


class TrieNode:
    def __init__(self):
        self.children = {}
        self.fail = None
        self.output = []


class ACAutomaton:
    def __init__(self):
        self.root = TrieNode()

    def add_word(self, pattern):
        """
        插入模式串到Trie树中
        :param pattern: 需要插入的模式串
        """
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.output.append(pattern)

    def build_failure_pointers(self):
        """
        构建AC自动机的失败指针
        """
        queue = deque()

        # 初始化root直接子节点的失败指针为root
        for char, node in self.root.children.items():
            node.fail = self.root
            queue.append(node)

        while queue:
            current_node = queue.popleft()

            # 遍历当前节点的所有子节点
            for char, next_node in current_node.children.items():
                # 找到current_node的失败指针
                fail_node = current_node.fail
                # 沿着失败指针查找，直到找到对应字符的子节点
                while fail_node is not None and char not in fail_node.children:
                    fail_node = fail_node.fail
                if fail_node is None:
                    next_node.fail = self.root
                else:
                    next_node.fail = fail_node.children[char]
                    next_node.output.extend(next_node.fail.output)
                queue.append(next_node)


class QABuffer:
    """
    整体思路：采用改进的 Aho-Corasick 自动机算法进行敏感词与角标的匹配工作，并执行相应的业务逻辑。
    Aho-Corasick 自动机是一种多模式匹配算法，广泛应用于敏感词匹配等业务场景；
    该算法在处理流式数据时表现尤为出色，因为它能在 O(1) 的时间复杂度内对每个输入字符完成匹配；
    与传统的正则表达式匹配相比，Aho-Corasick 自动机在匹配效率上具有显著优势，
    并且可以完全避免因构建缓冲区（buffer）而导致的输出延迟问题。
    """
    # 改进的正则表达式，支持单个数字和多个数字的引用格式
    corner_keyword_pattern = re.compile(r'\[citation:(\d+(?:\s*,\s*\d+)*)\]')

    def __init__(self, stream: AsyncIterable, keywords: List[str] = None, reference: list = None):
        self.buffer_deque = deque()  # 仅用于缓存潜在的匹配对象
        self.stream = stream
        self.content = ""
        self.thinking_flag = False
        self.keywords = set(keywords) if keywords else set()
        self.reference = reference

        self.ac = ACAutomaton()
        self.citation_content = {"[", "citation", ":"}
        self.citation_end = {"]"}
        for keyword in self.keywords | self.citation_content | self.citation_end:
            self.ac.add_word(keyword)

    async def generator(self):
        node = self.ac.root

        async for char, stage in self.stream:
            if stage == "thinking":
                yield Buffer(content=char, type_=BufferType.THINK_CONTENT)
                continue

            while node is not None and char not in node.children:
                node = node.fail

            if node is None:
                node = self.ac.root
                if "".join(list(self.buffer_deque)[0:11]).startswith("[citation:"):
                    self.buffer_deque.append(char)
                elif self.buffer_deque:
                    while self.buffer_deque:
                        yield self.yield_content(self.buffer_deque.popleft())
                    yield self.yield_content(char)
                else:
                    yield self.yield_content(char)
                continue

            node = node.children.get(char)

            # 判断匹配到了哪个模式
            if node.output:
                node_str = "".join(node.output).replace(" ", "")
                print(node_str)
                # 确实更浪费时间,但是有些模型会输出带空格的角标,或者连续输出][作为一个token
                if "[citation:" in node_str or "]" in node_str:
                    pattern = "[citation:" if "[citation:" in node_str else "]"
                else:
                    # if "<think>" in node.output:
                    #     self.thinking_flag = True
                    #     self.buffer_deque.clear()
                    #     continue
                    # if "</think>" in node.output:
                    #     self.thinking_flag = False
                    #     self.buffer_deque.clear()
                    #     continue

                    yield Buffer(content=",".join(node.output), type_=BufferType.SENSITIVE)
                    return

                if pattern == "[citation:":
                    self.buffer_deque.append(char)
                    continue
                elif pattern == "]":
                    self.buffer_deque.append(char)
                    chunk = "".join(self.buffer_deque).replace(" ", "")
                    if matches := self.find_corner_marks(chunk):
                        for start, end, refer_ids in matches:
                            # 为了防止返回的token类似 `][`的形式,不可直接clear列表,需要精准的从队列中拿出匹配到的信息
                            for ref_id in refer_ids:
                                yield Buffer(content=ref_id, type_=BufferType.CORNER)
                            remove_chars_count = end - start
                            while remove_chars_count > 0:
                                if char_len := len(self.buffer_deque[0]) <= remove_chars_count:
                                    self.buffer_deque.popleft()
                                    remove_chars_count -= char_len
                                else:
                                    self.buffer_deque[0] = self.buffer_deque[0][remove_chars_count:]
                                    break
                        node = self.ac.root
                    else:
                        while self.buffer_deque:
                            yield self.yield_content(self.buffer_deque.popleft())

            else:
                self.buffer_deque.append(char)

        while self.buffer_deque:
            yield self.yield_content(self.buffer_deque.popleft())

    def yield_content(self, char: str):
        # 在字符串末尾的URL
        pattern = r'(https?://[^\s]+|\b(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:/[^\s]*)?)$'
        self.content += char
        if char == "）" and re.search(pattern, self.content):
            return Buffer(content=" ）", type_=BufferType.CONTENT if not self.thinking_flag else BufferType.THINK_CONTENT)
        return Buffer(content=char, type_=BufferType.CONTENT if not self.thinking_flag else BufferType.THINK_CONTENT)

    @classmethod
    def find_corner_marks(cls, text: str) -> List[Tuple[int, int, List[str]]]:
        """
        在字符串中查找所有角标，并提取其中的数字。

        Args:
            text (str): 输入字符串。

        Returns:
            List[Tuple[int, int, str]]: 包含所有角标的元组列表，每个元组包含 (起始位置, 结束位置, 匹配文本)。
        """
        if not text:
            return []

        matches = []

        if result := cls.corner_keyword_pattern.finditer(text):
            for match in result:
                start, end = match.span()
                refs = match.group(1)
                ref_ids = [num.strip() for num in refs.split(',') if num.strip().isdigit()]
                # 已在 Buffer 的 generator 方法中处理多个引用ID
                matches.append((start, end, ref_ids))

        return matches


if __name__ == '__main__':
    import asyncio
    async def test_word():
        word = "中国人工智能科技创新Top 50[citation:1]。\n\n2. **产品与技术**"
        for w in word:
            yield w

    async def test():
        async for buffer in QABuffer(stream=test_word(), reference=[1, 2]).generator():
            print(buffer)

    asyncio.run(test())
