import re
from collections import deque
from collections.abc import AsyncIterable
from enum import Enum
from typing import List, Tuple

from pydantic import BaseModel


class BufferType(Enum):
    SENSITIVE = "sensitive"
    CORNER = "corner"
    CONTENT = "content"
    THINK_CONTENT = "think_content"
    THINK_CONTENT_CORNER = "think_content_corner"


class Buffer(BaseModel):
    content: str
    type_: BufferType


class TrieNode:
    def __init__(self):
        self.children = {}
        self.fail = None
        self.output = []


class ACAutomaton:
    def __init__(self):
        self.root = TrieNode()

    def add_word(self, pattern):
        """
        插入模式串到Trie树中
        :param pattern: 需要插入的模式串
        """
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.output.append(pattern)

    def build_failure_pointers(self):
        """
        构建AC自动机的失败指针
        """
        queue = deque()

        # 初始化root直接子节点的失败指针为root
        for char, node in self.root.children.items():
            node.fail = self.root
            queue.append(node)

        while queue:
            current_node = queue.popleft()

            # 遍历当前节点的所有子节点
            for char, next_node in current_node.children.items():
                # 找到current_node的失败指针
                fail_node = current_node.fail
                # 沿着失败指针查找，直到找到对应字符的子节点
                while fail_node is not None and char not in fail_node.children:
                    fail_node = fail_node.fail
                if fail_node is None:
                    next_node.fail = self.root
                else:
                    next_node.fail = fail_node.children[char]
                    next_node.output.extend(next_node.fail.output)
                queue.append(next_node)


class QABuffer:
    """
    整体思路：采用改进的 Aho-Corasick 自动机算法进行敏感词与角标的匹配工作，并执行相应的业务逻辑。
    Aho-Corasick 自动机是一种多模式匹配算法，广泛应用于敏感词匹配等业务场景；
    该算法在处理流式数据时表现尤为出色，因为它能在 O(1) 的时间复杂度内对每个输入字符完成匹配；
    与传统的正则表达式匹配相比，Aho-Corasick 自动机在匹配效率上具有显著优势，
    并且可以完全避免因构建缓冲区（buffer）而导致的输出延迟问题。
    """
    # 改进的正则表达式，支持单个数字和多个数字的引用格式
    corner_keyword_pattern = re.compile(r'\[citation:(\d+(?:\s*,\s*\d+)*)\]')

    def __init__(self, stream: AsyncIterable, keywords: List[str] = None, reference: list = None):
        self.buffer_deque = deque()  # 仅用于缓存潜在的匹配对象
        self.stream = stream
        self.content = ""
        self.thinking_flag = False
        self.keywords = set(keywords) if keywords else set()
        self.reference = reference

        self.ac = ACAutomaton()
        self.citation_content = {"[", "citation", ":"}
        self.citation_end = {"]"}
        for keyword in self.keywords | self.citation_content | self.citation_end:
            self.ac.add_word(keyword)

    async def generator(self):
        node = self.ac.root

        async for char, stage in self.stream:
            if stage == "thinking":
                yield Buffer(content=char, type_=BufferType.THINK_CONTENT)
                continue

            while node is not None and char not in node.children:
                node = node.fail

            if node is None:
                node = self.ac.root
                # 重新尝试从root开始匹配当前字符
                if char in node.children:
                    node = node.children[char]
                else:
                    # 当前字符无法匹配，检查是否需要缓存或输出
                    if self.buffer_deque and self._has_potential_citation():
                        # 如果缓冲区有潜在citation，继续缓存
                        self.buffer_deque.append(char)
                        continue
                    else:
                        # 输出缓冲区内容和当前字符
                        while self.buffer_deque:
                            yield self.yield_content(self.buffer_deque.popleft())
                        yield self.yield_content(char)
                        continue
            else:
                node = node.children.get(char)

            # 检查是否匹配到citation相关模式
            if node and node.output:
                matched_patterns = node.output
                # 检查是否是敏感词
                sensitive_words = [word for word in matched_patterns if word not in self.citation_content and word not in self.citation_end]
                if sensitive_words:
                    yield Buffer(content=",".join(sensitive_words), type_=BufferType.SENSITIVE)
                    return

                # 如果匹配到citation相关字符，添加到缓冲区
                if any(word in self.citation_content or word in self.citation_end for word in matched_patterns):
                    self.buffer_deque.append(char)

                    # 检查是否匹配到"]"，如果是则尝试完整匹配
                    if "]" in matched_patterns:
                        buffer_text = "".join(self.buffer_deque)
                        if matches := self.find_corner_marks(buffer_text):
                            # 找到完整的citation，输出引用ID
                            for start, end, refer_ids in matches:
                                for ref_id in refer_ids:
                                    yield Buffer(content=ref_id, type_=BufferType.CORNER)
                            # 清理缓冲区
                            self.buffer_deque.clear()
                            node = self.ac.root
                        else:
                            # 没有找到完整citation，输出缓冲区内容
                            while self.buffer_deque:
                                yield self.yield_content(self.buffer_deque.popleft())
                            node = self.ac.root
                    continue

            # 如果没有匹配到任何模式，添加到缓冲区
            self.buffer_deque.append(char)

            # 防止缓冲区无限增长
            if len(self.buffer_deque) > 20 and not self._has_potential_citation():
                yield self.yield_content(self.buffer_deque.popleft())

        while self.buffer_deque:
            yield self.yield_content(self.buffer_deque.popleft())

    def _has_potential_citation(self) -> bool:
        """检查缓冲区是否包含潜在的citation模式"""
        buffer_text = "".join(self.buffer_deque)
        return '[' in buffer_text and ('citation' in buffer_text or 'c' in buffer_text)

    def yield_content(self, char: str):
        # 在字符串末尾的URL
        pattern = r'(https?://[^\s]+|\b(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:/[^\s]*)?)$'
        self.content += char
        if char == "）" and re.search(pattern, self.content):
            return Buffer(content=" ）", type_=BufferType.CONTENT if not self.thinking_flag else BufferType.THINK_CONTENT)
        return Buffer(content=char, type_=BufferType.CONTENT if not self.thinking_flag else BufferType.THINK_CONTENT)

    @classmethod
    def find_corner_marks(cls, text: str) -> List[Tuple[int, int, List[str]]]:
        """
        在字符串中查找所有角标，并提取其中的数字。

        Args:
            text (str): 输入字符串。

        Returns:
            List[Tuple[int, int, str]]: 包含所有角标的元组列表，每个元组包含 (起始位置, 结束位置, 匹配文本)。
        """
        if not text:
            return []

        matches = []

        if result := cls.corner_keyword_pattern.finditer(text):
            for match in result:
                start, end = match.span()
                refs = match.group(1)
                ref_ids = [num.strip() for num in refs.split(',') if num.strip().isdigit()]
                # 已在 Buffer 的 generator 方法中处理多个引用ID
                matches.append((start, end, ref_ids))

        return matches


if __name__ == '__main__':
    import asyncio
    async def test_word():
        word = "中国人工智能科技创新Top 50[citation:1]。\n\n2. **产品与技术**"
        for w in word:
            yield w

    async def test():
        async for buffer in QABuffer(stream=test_word(), reference=[1, 2]).generator():
            print(buffer)

    asyncio.run(test())
