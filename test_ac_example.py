from collections import deque

class TrieNode:
    def __init__(self):
        self.children = {}
        self.fail = None
        self.output = []

class ACAutomaton:
    def __init__(self):
        self.root = TrieNode()

    def add_word(self, pattern):
        """插入模式串到Trie树中"""
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.output.append(pattern)

    def build_failure_pointers(self):
        """构建AC自动机的失败指针"""
        queue = deque()
        
        # 初始化root直接子节点的失败指针为root
        for char, node in self.root.children.items():
            node.fail = self.root
            queue.append(node)

        while queue:
            current_node = queue.popleft()
            
            # 遍历当前节点的所有子节点
            for char, next_node in current_node.children.items():
                # 找到current_node的失败指针
                fail_node = current_node.fail
                # 沿着失败指针查找，直到找到对应字符的子节点
                while fail_node is not None and char not in fail_node.children:
                    fail_node = fail_node.fail
                if fail_node is None:
                    next_node.fail = self.root
                else:
                    next_node.fail = fail_node.children[char]
                    next_node.output.extend(next_node.fail.output)
                queue.append(next_node)

def test_ac_automaton():
    """测试AC自动机匹配citation模式"""
    
    # 模拟你的配置
    citation_content = {"[", "citation", ":"}
    citation_end = {"]"}
    
    # 创建AC自动机
    ac = ACAutomaton()
    
    # 添加模式
    for keyword in citation_content | citation_end:
        ac.add_word(keyword)
    
    # 构建失败指针
    ac.build_failure_pointers()
    
    # 打印AC自动机结构
    print("AC自动机结构:")
    print(f"Root children: {list(ac.root.children.keys())}")
    for char, node in ac.root.children.items():
        print(f"  '{char}' -> output: {node.output}, children: {list(node.children.keys())}")
        for sub_char, sub_node in node.children.items():
            print(f"    '{sub_char}' -> output: {sub_node.output}, children: {list(sub_node.children.keys())}")
    
    # 测试字符串
    test_text = "[citation:1]"
    print(f"\n测试文本: {test_text}")
    print("=" * 50)
    
    # 模拟匹配过程
    node = ac.root
    buffer_deque = deque()
    
    for i, char in enumerate(test_text):
        print(f"\n步骤 {i+1}: 处理字符 '{char}'")
        print(f"  当前缓冲区: '{''.join(buffer_deque)}'")
        print(f"  当前node是否为root: {node is ac.root}")
        
        # AC自动机状态转移
        original_node = node
        while node is not None and char not in node.children:
            print(f"  失败指针跳转: {node} -> {node.fail}")
            node = node.fail
        
        if node is None:
            print(f"  重置到root")
            node = ac.root
            # 重新尝试从root开始匹配当前字符
            if char in node.children:
                print(f"  从root匹配成功: '{char}'")
                node = node.children[char]
                # 如果是citation相关字符，添加到缓冲区
                if char in citation_content | citation_end:
                    buffer_deque.append(char)
                    print(f"  添加到缓冲区: '{char}'")
            else:
                print(f"  从root也无法匹配: '{char}'")
                if char in citation_content | citation_end:
                    buffer_deque.append(char)
                    print(f"  强制添加到缓冲区: '{char}'")
                else:
                    print(f"  输出字符: '{char}'")
        else:
            print(f"  状态转移: {original_node} -> {node.children.get(char)}")
            node = node.children.get(char)
            if char in citation_content | citation_end:
                buffer_deque.append(char)
                print(f"  添加到缓冲区: '{char}'")
        
        # 检查匹配输出
        if node and node.output:
            print(f"  匹配到模式: {node.output}")
        else:
            print(f"  无匹配输出")
        
        print(f"  最终缓冲区: '{''.join(buffer_deque)}'")
    
    print(f"\n最终结果:")
    print(f"缓冲区内容: '{''.join(buffer_deque)}'")

if __name__ == "__main__":
    test_ac_automaton()
